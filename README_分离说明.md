# PUF Authentication System - 代码分离说明

## 概述

原始的 `puf.py` 程序已成功分离为两个独立的模块：

1. **核心训练模块** (`puf_core.py`) - 包含神经网络训练的核心功能
2. **GUI界面模块** (`puf_gui.py`) - 包含用户界面相关的代码

## 文件结构

```
physicalUncloneFunction/
├── puf.py                    # 原始程序（保留）
├── puf_core.py              # 神经网络训练核心模块
├── puf_gui.py               # GUI界面模块
├── main.py                  # 新的主程序入口
├── test_structure.py        # 代码结构测试脚本
├── README_分离说明.md        # 本说明文件
└── results/                 # 结果目录
    ├── trained_models/      # 训练好的模型
    ├── sample_images/       # 样本图像
    ├── training_logs/       # 训练日志
    └── preview_samples/     # 预览样本
```

## 模块详细说明

### 1. 核心训练模块 (`puf_core.py`)

包含以下核心功能：

#### 工具函数
- `get_optimal_workers()` - 获取最优工作线程数
- `get_user_data_path()` - 获取用户数据目录
- `ensure_user_directories()` - 确保目录存在
- `check_gpu_status()` - GPU状态检查
- `set_seed()` - 设置随机种子

#### 图像处理
- `preprocess_image_cpu()` - CPU图像预处理
- `preprocess_image()` - 图像预处理接口

#### 数据集
- `PUFDataset` - 数据集类，支持数据增强

#### 模型相关
- `create_model()` - 创建AlexNet模型
- `GPUMemoryManager` - GPU内存管理
- `GPUTrainer` - GPU训练器
- `CPUTrainer` - CPU训练器

#### 训练和验证
- `train_model_optimized()` - 优化的训练函数
- `verify_image_optimized()` - 图像验证函数
- `plot_training_history()` - 绘制训练历史

### 2. GUI界面模块 (`puf_gui.py`)

包含以下界面功能：

#### 界面组件
- `TextRedirector` - 文本重定向类
- `PUFAuthenticationApp` - 主应用程序类

#### 界面功能
- 训练参数设置
- 图像选择和预览
- 实时训练进度显示
- 模型验证界面
- 日志显示

#### 训练控制
- 训练线程管理
- 强制停止功能
- 图表更新

## 使用方法

### 启动分离后的程序
```bash
python main.py
```

### 启动原始程序（保留）
```bash
python puf.py
```

### 测试代码结构
```bash
python test_structure.py
```

## 分离的优势

1. **模块化设计** - 核心功能和界面分离，便于维护
2. **代码复用** - 核心模块可以独立使用
3. **易于扩展** - 可以轻松添加新的界面或训练方法
4. **便于测试** - 可以单独测试核心功能
5. **清晰结构** - 代码组织更加清晰

## 依赖关系

- `puf_gui.py` 依赖 `puf_core.py`
- `main.py` 依赖 `puf_gui.py`
- 所有模块都需要相同的第三方库（PyTorch, PIL, matplotlib等）

## 注意事项

1. 确保所有依赖库已正确安装
2. 分离后的程序功能与原始程序完全一致
3. 原始 `puf.py` 文件保留，可以继续使用
4. 新程序通过 `main.py` 启动

## 开发建议

- 核心功能修改请在 `puf_core.py` 中进行
- 界面相关修改请在 `puf_gui.py` 中进行
- 添加新功能时保持模块间的清晰分离
