
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named org - imported by copy (optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), tqdm.utils (delayed, optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), http.server (delayed, optional), webbrowser (delayed), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional), setuptools.sandbox (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by os (conditional, optional), shutil (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed), distutils.archive_util (optional)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _distutils_hack - imported by G:\physicalUncloneFunction\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_setuptools.py (delayed, conditional)
missing module named StringIO - imported by six (conditional), setuptools._vendor.six (conditional), pkg_resources._vendor.six (conditional)
missing module named _uuid - imported by uuid (optional)
missing module named netbios - imported by uuid (delayed)
missing module named win32wnet - imported by uuid (delayed)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), pkg_resources._vendor.appdirs (delayed, conditional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), pstats (conditional, optional), rlcompleter (optional), sympy.interactive.session (delayed, optional), site (delayed, optional)
missing module named __builtin__ - imported by pkg_resources._vendor.pyparsing (conditional), setuptools._vendor.pyparsing (conditional)
missing module named ordereddict - imported by pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named collections.MutableMapping - imported by collections (optional), pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named collections.Iterable - imported by collections (optional), pkg_resources._vendor.pyparsing (optional), setuptools._vendor.pyparsing (optional)
missing module named _manylinux - imported by pkg_resources._vendor.packaging.tags (delayed, optional), packaging._manylinux (delayed, optional), setuptools._vendor.packaging.tags (delayed, optional)
missing module named 'setuptools._vendor.six.moves'.parse - imported by 'setuptools._vendor.six.moves' (top-level), setuptools._vendor.packaging.requirements (top-level)
runtime module named setuptools.extern.six.moves - imported by setuptools.dist (top-level), configparser (top-level), setuptools.command.easy_install (top-level), setuptools.sandbox (top-level), setuptools.command.setopt (top-level), setuptools.package_index (top-level), setuptools.ssl_support (top-level), setuptools.py33compat (top-level), setuptools.command.egg_info (top-level), setuptools.command.py36compat (top-level), setuptools.msvc (top-level), 'setuptools._vendor.six.moves' (top-level)
missing module named setuptools.extern.six.moves.filterfalse - imported by setuptools.extern.six.moves (top-level), setuptools.dist (top-level), setuptools.msvc (top-level)
missing module named setuptools.extern.six.moves.filter - imported by setuptools.extern.six.moves (top-level), setuptools.dist (top-level), setuptools.ssl_support (top-level), setuptools.command.py36compat (top-level)
missing module named setuptools.extern.six.moves.map - imported by setuptools.extern.six.moves (top-level), setuptools.dist (top-level), setuptools.command.easy_install (top-level), setuptools.sandbox (top-level), setuptools.package_index (top-level), setuptools.ssl_support (top-level), setuptools.command.egg_info (top-level)
missing module named setuptools.extern.six - imported by setuptools.extern (top-level), setuptools.monkey (top-level), setuptools.dist (top-level), setuptools.extern.six.moves (top-level), setuptools.config (top-level), setuptools.command.easy_install (top-level), setuptools.sandbox (top-level), setuptools.py27compat (top-level), setuptools.package_index (top-level), setuptools.py33compat (top-level), setuptools.wheel (top-level), setuptools.command.egg_info (top-level), setuptools.command.sdist (top-level), setuptools.command.bdist_egg (top-level), setuptools.unicode_utils (top-level), setuptools.installer (top-level), setuptools.command.build_ext (top-level)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools.command.sdist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools.command.egg_info (top-level)
missing module named 'setuptools._vendor.six.moves' - imported by 'setuptools._vendor.six.moves' (top-level)
missing module named 'setuptools.extern.pyparsing' - imported by setuptools._vendor.packaging.markers (top-level), setuptools._vendor.packaging.requirements (top-level)
missing module named collections.Sequence - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named collections.MutableSet - imported by collections (optional), setuptools._vendor.ordered_set (optional)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config (top-level), setuptools.msvc (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named 'pkg_resources.extern.pyparsing' - imported by pkg_resources._vendor.packaging.markers (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named win32api - imported by torch._appdirs (delayed, conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named 'win32com.shell' - imported by torch._appdirs (conditional, optional), pkg_resources._vendor.appdirs (delayed, conditional, optional)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.appdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.six.moves' - imported by pkg_resources (top-level), pkg_resources._vendor.packaging.requirements (top-level)
missing module named pkg_resources.extern.six - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config (top-level)
missing module named wincertstore - imported by setuptools.ssl_support (delayed, optional)
missing module named 'backports.ssl_match_hostname' - imported by setuptools.ssl_support (optional)
missing module named backports - imported by setuptools.ssl_support (optional)
missing module named 'setuptools.extern.six' - imported by setuptools (top-level), setuptools.extension (top-level)
missing module named pyimod02_importers - imported by G:\physicalUncloneFunction\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), G:\physicalUncloneFunction\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level), numpy.ctypeslib (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isinf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.product - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), torch.testing._internal.common_device_type (optional), torch._numpy.testing.utils (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level), torch._dynamo.variables.misc (optional)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._philox (top-level), numpy.random._sfc64 (top-level), numpy.random._generator (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random.bit_generator (top-level), numpy.random._generator (top-level), numpy.random._mt19937 (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named 'tornado.template' - imported by matplotlib.backends.backend_webagg (delayed)
missing module named 'tornado.websocket' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.ioloop' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named 'tornado.web' - imported by matplotlib.backends.backend_webagg (top-level)
missing module named tornado - imported by matplotlib.backends.backend_webagg (optional), matplotlib.backends.backend_webagg_core (delayed)
missing module named matplotlib.axes.Axes - imported by matplotlib.axes (delayed), matplotlib.legend (delayed), matplotlib.projections.geo (top-level), matplotlib.projections.polar (top-level), mpl_toolkits.mplot3d.axes3d (top-level), matplotlib.figure (top-level), matplotlib.pyplot (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named 'numpy.exceptions' - imported by matplotlib.cbook (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named certifi - imported by matplotlib (delayed, optional)
missing module named setuptools_scm - imported by matplotlib (delayed, conditional), tqdm.version (optional)
missing module named pycocotools - imported by torchvision.datasets.coco (delayed), torchvision.tv_tensors._dataset_wrapper (delayed)
missing module named accimage - imported by torchvision.transforms.transforms (optional), torchvision.transforms.functional (optional), torchvision.transforms._functional_pil (optional), torchvision.datasets.folder (delayed)
missing module named av - imported by torchvision.io.video (optional), torchvision.io.video_reader (optional)
missing module named 'defusedxml.ElementTree' - imported by torchvision.datasets.voc (optional)
missing module named gdown - imported by torchvision.datasets.utils (delayed, optional)
missing module named 'scipy.io' - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), torchvision.datasets.caltech (delayed), torchvision.datasets.flowers102 (delayed), torchvision.datasets.imagenet (delayed), torchvision.datasets.sbd (delayed, optional), torchvision.datasets.stanford_cars (delayed, optional), torchvision.datasets.svhn (delayed)
missing module named h5py - imported by torchvision.datasets.pcam (delayed, optional)
missing module named lmdb - imported by torchvision.datasets.lsun (delayed)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), torchvision.datasets.kinetics (top-level)
missing module named torch.device - imported by torch (top-level), torch.cuda (top-level), torch._library.infer_schema (top-level), torch.xpu (top-level), torch.distributed.nn.api.remote_module (top-level), torch.nn.modules.module (top-level), torch.cpu (top-level), torch.mtia (top-level)
missing module named tabulate - imported by torch.fx.graph (delayed, optional), torch.ao.ns.fx.n_shadows_utils (delayed, optional), torch._inductor.wrapper_benchmark (delayed), torch.utils.flop_counter (delayed), torch._dynamo.utils (delayed, optional), torch._dynamo.backends.distributed (delayed, conditional, optional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tensor.debug._op_coverage (delayed), torch.distributed._tensor.debug.visualize_sharding (delayed, optional), torch.utils.benchmark.utils.compile (optional)
missing module named dill - imported by torch.utils._import_utils (delayed), torch.utils.data.graph (delayed, conditional, optional)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named pandas - imported by tqdm.std (delayed, optional), networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.algorithms.centrality.group (delayed), torch.utils.data.datapipes.dataframe.dataframe_wrapper (delayed, optional), fsspec.implementations.reference (delayed)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (optional)
missing module named torch.tensor - imported by torch (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'triton.runtime' - imported by torch.utils._triton (delayed), torch._inductor.runtime.triton_heuristics (delayed, conditional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.select_algorithm (delayed, optional), torch._inductor.ir (delayed), torch._dynamo.variables.builder (delayed, conditional), torch._dynamo.variables.functions (delayed), torch._inductor.utils (delayed), torch._inductor.codecache (delayed, conditional, optional), torch._inductor.compile_fx (delayed, optional)
missing module named 'triton.compiler' - imported by torch.utils._triton (delayed), torch._inductor.runtime.hints (optional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.codegen.triton (delayed), torch._inductor.scheduler (delayed), torch._inductor.codecache (delayed, optional), torch._inductor.async_compile (delayed, optional)
missing module named triton - imported by torch._utils_internal (delayed, conditional), torch._dynamo.logging (conditional, optional), torch.utils._triton (delayed, optional), torch.sparse._triton_ops_meta (delayed, conditional), torch.sparse._triton_ops (conditional), torch._inductor.runtime.coordinate_descent_tuner (optional), torch._inductor.runtime.triton_heuristics (conditional, optional), torch._higher_order_ops.triton_kernel_wrap (delayed), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.codegen.wrapper (delayed, conditional), torch._dynamo.utils (conditional), torch._inductor.compile_worker.__main__ (optional), torch._inductor.runtime.triton_helpers (optional), torch.testing._internal.triton_utils (conditional)
missing module named 'torch._C._profiler' - imported by torch.utils._traceback (delayed), torch.profiler (top-level), torch.autograd.profiler (top-level), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.cuda._memory_viz (delayed), torch.testing._internal.logging_tensor (top-level), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named numpy.uint - imported by numpy (top-level), numpy.random.mtrand (top-level), numpy.random._generator (top-level)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
missing module named lxml - imported by sympy.utilities.mathml (delayed)
missing module named pytest - imported by sympy.testing.runtests_pytest (optional), networkx.classes.backends (conditional, optional), torch.testing._internal.common_utils (delayed, conditional, optional), torch.testing._internal.optests.generate_tests (delayed, conditional), torch._numpy.testing.utils (delayed), fsspec.conftest (top-level)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'IPython.core' - imported by sympy.interactive.printing (delayed, optional)
missing module named IPython - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional, optional)
missing module named 'scipy.special' - imported by sympy.functions.special.bessel (delayed, conditional, optional), networkx.generators.community (delayed, optional), torch.testing._internal.opinfo.definitions.special (conditional), torch.testing._internal.common_methods_invocations (conditional)
missing module named scipy - imported by sympy.functions.special.bessel (delayed, conditional), networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.generators.geometric (delayed, optional), networkx.algorithms.assortativity.correlation (delayed), networkx.algorithms.centrality.flow_matrix (delayed), networkx.algorithms.centrality.eigenvector (delayed), networkx.algorithms.centrality.subgraph_alg (delayed), networkx.algorithms.centrality.laplacian (delayed), networkx.algorithms.communicability_alg (delayed), networkx.algorithms.distance_measures (delayed), networkx.algorithms.link_analysis.hits_alg (delayed), networkx.algorithms.link_analysis.pagerank_alg (delayed), networkx.algorithms.similarity (delayed), networkx.algorithms.approximation.traveling_salesman (delayed), networkx.algorithms.bipartite.matrix (delayed), networkx.algorithms.bipartite.matching (delayed), networkx.algorithms.bipartite.spectral (delayed), networkx.algorithms.node_classification (delayed), networkx.generators.random_graphs (delayed, conditional), networkx.generators.spectral_graph_forge (delayed), networkx.linalg.attrmatrix (delayed), networkx.linalg.spectrum (delayed), networkx.linalg.graphmatrix (delayed), networkx.linalg.laplacianmatrix (delayed), networkx.linalg.algebraicconnectivity (delayed, conditional), networkx.linalg.bethehessianmatrix (delayed), networkx.drawing.layout (delayed), torch.testing._internal.common_methods_invocations (conditional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named optree - imported by torch.utils._cxx_pytree (top-level)
missing module named 'triton.language' - imported by torch.sparse._triton_ops (conditional), torch._inductor.codegen.triton_split_scan (delayed), torch._inductor.codegen.wrapper (delayed), torch._inductor.runtime.triton_helpers (optional), torch.testing._internal.triton_utils (conditional)
missing module named 'hypothesis.strategies' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named hypothesis - imported by torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named 'scipy.signal' - imported by torch.testing._internal.opinfo.definitions.signal (conditional)
missing module named 'scipy.fft' - imported by torch.testing._internal.opinfo.definitions.fft (conditional, optional)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.distributed_c10d (top-level), torch.distributed.constants (top-level), torch.distributed.rpc (conditional), torch._dynamo.variables.distributed (delayed), torch.testing._internal.distributed.fake_pg (top-level), torch.distributed._cuda_p2p (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch.distributed.elastic.control_plane (delayed), torch.testing._internal.distributed.multi_threaded_pg (top-level)
missing module named fcntl - imported by tqdm.utils (delayed, optional), filelock._unix (conditional, optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named xmlrunner - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named 'scipy.spatial' - imported by networkx.generators.geometric (delayed, optional), torch.testing._internal.common_methods_invocations (conditional)
missing module named transformers - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.dynamo_graph_extractor (delayed, optional), torch._dynamo.variables.dicts (delayed), torch.testing._internal.common_distributed (delayed, optional)
missing module named numba - imported by torch.testing._internal.common_cuda (conditional, optional)
missing module named 'torch._C._autograd' - imported by torch._subclasses.meta_utils (top-level), torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.autograd (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.fx.passes.utils.common (top-level), torch.optim.swa_utils (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.ao.quantization.fake_quantize (top-level), torch._dynamo.mutation_guard (top-level), torch.distributed.nn.api.remote_module (top-level)
missing module named onnx - imported by torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.exporter (delayed, conditional, optional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (optional), torch.utils.tensorboard._onnx_graph (delayed), torch.onnx.verification (delayed, optional)
missing module named onnxruntime - imported by torch.onnx._internal.exporter (delayed, conditional), torch.onnx._internal.onnxruntime (optional), torch.onnx.verification (delayed, optional)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx.utils (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxscript.rewriter' - imported by torch.onnx._internal.onnxruntime (delayed, conditional, optional)
missing module named onnxscript - imported by torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.exporter (delayed, conditional, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level), torch.onnx._internal.fx.op_validation (top-level), torch.onnx._internal.onnxruntime (delayed, conditional, optional)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (optional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named safetensors - imported by torch.onnx._internal.fx.patcher (delayed, conditional, optional), torch.onnx._internal.fx.serialization (delayed)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter (delayed, conditional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (top-level), torch.onnx._internal.fx.decomposition_skip (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named beartype - imported by torch.onnx._internal._beartype (optional)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.Size - imported by torch (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'tensorboard.summary' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard (top-level)
missing module named 'google.protobuf' - imported by torch.utils.tensorboard.writer (delayed)
missing module named moviepy - imported by torch.utils.tensorboard.summary (delayed, optional)
missing module named google - imported by torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.plugins' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard.summary (top-level)
missing module named 'tensorboard.compat' - imported by torch.utils.tensorboard.writer (top-level), torch.utils.tensorboard._embedding (top-level), torch.utils.tensorboard._onnx_graph (top-level), torch.utils.tensorboard._pytorch_graph (top-level), torch.utils.tensorboard._proto_graph (top-level), torch.utils.tensorboard.summary (top-level)
missing module named tensorboard - imported by torch.utils.tensorboard (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named astunparse - imported by torch.jit.frontend (optional), torch._dynamo.guards (conditional, optional)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named pydot - imported by torch.fx.passes.graph_drawer (optional), networkx.drawing.nx_pydot (delayed), torch._functorch.partitioners (delayed)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (top-level), torch.distributed.elastic.rendezvous.etcd_store (top-level), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (top-level), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named urllib3 - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (top-level)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named zstandard - imported by fsspec.compression (optional)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow.parquet' - imported by fsspec.parquet (delayed)
missing module named fastparquet - imported by fsspec.parquet (delayed)
missing module named 'requests.auth' - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named requests - imported by fsspec.implementations.dbfs (top-level), fsspec.implementations.github (top-level), fsspec.implementations.http_sync (delayed, optional), fsspec.implementations.jupyter (top-level), fsspec.implementations.webhdfs (top-level)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named js - imported by fsspec.implementations.http_sync (delayed, optional)
missing module named yarl - imported by fsspec.implementations.http (top-level), fsspec.implementations.http_sync (optional)
missing module named aiohttp - imported by fsspec.implementations.http (top-level)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'requests.adapters' - imported by fsspec.implementations.dbfs (top-level)
missing module named 'requests.exceptions' - imported by fsspec.implementations.dbfs (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by fsspec.implementations.dask (top-level)
missing module named 'pyarrow.fs' - imported by fsspec.implementations.arrow (delayed)
missing module named pyarrow - imported by fsspec.implementations.arrow (delayed)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named 'torch_xla.distributed' - imported by torch.distributed._tensor.api (delayed, conditional, optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named amdsmi - imported by torch.cuda (optional), torch.cuda.memory (delayed, conditional, optional)
missing module named pynvml - imported by torch.cuda (delayed, optional), torch.cuda.memory (delayed, conditional, optional)
missing module named 'tensorflow.python' - imported by torch.contrib._tensorboard_vis (optional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named tensorflow - imported by torch.contrib._tensorboard_vis (optional)
missing module named opt_einsum - imported by torch.backends.opt_einsum (optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._higher_order_ops.cond (top-level), torch._functorch.utils (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.vmap (top-level), torch._functorch.eager_transforms (top-level), torch._functorch.autograd_function (top-level), torch._higher_order_ops.associative_scan (top-level), torch._subclasses.fake_tensor (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.any - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.all - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'triton.testing' - imported by torch._functorch.partitioners (delayed, conditional), torch._inductor.runtime.runtime_utils (delayed, optional), torch._inductor.utils (delayed)
missing module named 'triton.backends' - imported by torch._inductor.runtime.triton_heuristics (conditional, optional)
missing module named redis - imported by torch._inductor.remote_cache (delayed)
missing module named 'torch._inductor.fb' - imported by torch._inductor.graph (conditional), torch._inductor.codecache (conditional), torch._inductor.cpp_builder (conditional), torch._inductor.compile_fx (conditional)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes (delayed, conditional), torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'triton.fb' - imported by torch._inductor.codecache (conditional), torch._inductor.cpp_builder (conditional)
missing module named 'torch.utils._config_typing' - imported by torch._dynamo.config (conditional), torch._inductor.config (conditional), torch._functorch.config (conditional)
missing module named libfb - imported by torch._inductor.config (conditional)
missing module named 'cutlass_library.gemm_operation' - imported by torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.library' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional), torch._inductor.codegen.cuda.gemm_template (delayed), torch._inductor.codegen.cuda.cutlass_lib_extensions.gemm_operation_extensions (conditional)
missing module named 'cutlass_library.generator' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed)
missing module named 'cutlass_library.manifest' - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named cutlass_library - imported by torch._inductor.codegen.cuda.cutlass_utils (delayed, conditional, optional)
missing module named 'libfb.py' - imported by torch._dynamo.debug_utils (conditional), torch._inductor.codecache (delayed, conditional)
missing module named 'scipy.optimize' - imported by networkx.algorithms.similarity (delayed), networkx.algorithms.bipartite.matching (delayed), networkx.generators.random_graphs (delayed, conditional), networkx.drawing.layout (delayed), torch._functorch.partitioners (delayed, optional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'scipy.sparse' - imported by networkx.convert_matrix (delayed), networkx.algorithms.centrality.flow_matrix (delayed), networkx.algorithms.centrality.eigenvector (delayed), networkx.algorithms.distance_measures (delayed), networkx.algorithms.link_analysis.hits_alg (delayed), networkx.algorithms.link_analysis.pagerank_alg (delayed), networkx.algorithms.bipartite.matrix (delayed), networkx.algorithms.node_classification (delayed), networkx.linalg.attrmatrix (delayed), networkx.linalg.graphmatrix (delayed), networkx.linalg.laplacianmatrix (delayed), networkx.linalg.algebraicconnectivity (delayed, conditional), networkx.linalg.bethehessianmatrix (delayed), networkx.drawing.layout (delayed)
missing module named 'scipy.linalg' - imported by networkx.algorithms.centrality.subgraph_alg (delayed), networkx.algorithms.centrality.laplacian (delayed), networkx.algorithms.communicability_alg (delayed), networkx.algorithms.bipartite.spectral (delayed), networkx.linalg.spectrum (delayed), networkx.linalg.algebraicconnectivity (delayed)
missing module named 'scipy.stats' - imported by networkx.algorithms.assortativity.correlation (delayed), networkx.generators.spectral_graph_forge (delayed)
missing module named 'lxml.etree' - imported by networkx.readwrite.graphml (delayed, optional)
missing module named torch_xla - imported by torch._functorch.fx_minifier (delayed)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named yaml - imported by torch._export.serde.schema_check (delayed, conditional)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed)
missing module named diffusers - imported by torch._dynamo.variables.dicts (delayed, optional)
missing module named 'transformers.file_utils' - imported by torch._dynamo.variables.dicts (delayed, optional)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.types (conditional), torch._dynamo.decorators (conditional)
missing module named 'torch_xla.core' - imported by torch._dynamo.testing (delayed, conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named 'einops._torch_specific' - imported by torch._dynamo.decorators (delayed, optional)
missing module named einops - imported by torch._dynamo.decorators (delayed)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named com - imported by torch._appdirs (delayed)
missing module named win32com - imported by torch._appdirs (delayed)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named cffi - imported by PIL.Image (optional), PIL.PyAccess (optional)
missing module named defusedxml - imported by PIL.Image (optional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
