<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="12">
            <item index="0" class="java.lang.String" itemvalue="dashscope" />
            <item index="1" class="java.lang.String" itemvalue="msoffcrypto-tool" />
            <item index="2" class="java.lang.String" itemvalue="openai" />
            <item index="3" class="java.lang.String" itemvalue="pillow" />
            <item index="4" class="java.lang.String" itemvalue="markdown" />
            <item index="5" class="java.lang.String" itemvalue="minio" />
            <item index="6" class="java.lang.String" itemvalue="gevent" />
            <item index="7" class="java.lang.String" itemvalue="dash" />
            <item index="8" class="java.lang.String" itemvalue="gunicorn" />
            <item index="9" class="java.lang.String" itemvalue="pyjwt" />
            <item index="10" class="java.lang.String" itemvalue="sqlalchemy" />
            <item index="11" class="java.lang.String" itemvalue="dash-bootstrap-components" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N802" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="pygame" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>