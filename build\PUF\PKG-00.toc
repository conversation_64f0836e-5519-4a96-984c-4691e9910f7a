('G:\\physicalUncloneFunction\\build\\PUF\\PUFAuth.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'G:\\physicalUncloneFunction\\build\\PUF\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'G:\\physicalUncloneFunction\\build\\PUF\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'G:\\physicalUncloneFunction\\build\\PUF\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'G:\\physicalUncloneFunction\\build\\PUF\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'G:\\physicalUncloneFunction\\build\\PUF\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'G:\\physicalUncloneFunction\\build\\PUF\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'g:\\physicalunclonefunction\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'G:\\physicalUncloneFunction\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('PUF', 'G:\\physicalUncloneFunction\\PUF.py', 'PYSOURCE')],
 'python38.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
